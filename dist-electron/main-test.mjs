import { app, dialog, <PERSON><PERSON>er<PERSON>indow, <PERSON>u, shell } from "electron";
import { fileURLToPath } from "node:url";
import path from "node:path";
import { l as logger } from "./logger-CDSbv2DK.js";
const __dirname = path.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = app.getAppPath();
const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
if (process.platform === "win32") {
  app.disableHardwareAcceleration();
}
if (process.platform === "win32") {
  app.setAppUserModelId(app.getName());
}
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}
let win = null;
const preload = path.join(__dirname, "../preload/preload.mjs");
const indexHtml = path.join(RENDERER_DIST, "index.html");
function createApplicationMenu() {
  const template = [
    {
      label: "File",
      submenu: [
        {
          label: "Test Frontend",
          accelerator: "CmdOrCtrl+T",
          click: () => {
            win?.webContents.send("menu-action", "test-frontend");
          }
        },
        { type: "separator" },
        {
          label: "Exit",
          accelerator: process.platform === "darwin" ? "Cmd+Q" : "Ctrl+Q",
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: "View",
      submenu: [
        { role: "reload" },
        { role: "forceReload" },
        { role: "toggleDevTools" },
        { type: "separator" },
        { role: "resetZoom" },
        { role: "zoomIn" },
        { role: "zoomOut" },
        { type: "separator" },
        { role: "togglefullscreen" }
      ]
    }
  ];
  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}
async function createWindow() {
  win = new BrowserWindow({
    title: "Woodpecker API - Frontend Test",
    icon: path.join(process.env.VITE_PUBLIC, "favicon.ico"),
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    center: true,
    show: false,
    // Don't show until ready
    titleBarStyle: process.platform === "darwin" ? "hiddenInset" : "default",
    webPreferences: {
      preload,
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      sandbox: false,
      // Required for IPC bridge
      spellcheck: false
    }
  });
  win.once("ready-to-show", () => {
    if (win) {
      win.show();
      if (process.platform === "darwin") {
        win.focus();
      }
    }
  });
  win.on("closed", () => {
    win = null;
  });
  try {
    if (VITE_DEV_SERVER_URL) {
      await win.loadURL(VITE_DEV_SERVER_URL);
      win.webContents.openDevTools();
    } else {
      await win.loadFile(indexHtml);
    }
  } catch (error) {
    logger.error("Window", "Failed to load application content", error instanceof Error ? error : new Error(String(error)));
    throw error;
  }
  win.webContents.on("did-finish-load", () => {
    logger.info("Window", "Application content loaded successfully");
    win?.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  win.webContents.on("did-fail-load", (event, errorCode, errorDescription) => {
    logger.error("Window", `Failed to load content: ${errorDescription} (${errorCode})`);
  });
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith("https:") || url.startsWith("http:")) {
      shell.openExternal(url);
    }
    return { action: "deny" };
  });
}
app.whenReady().then(async () => {
  try {
    logger.info("App", "Frontend test application starting up");
    createApplicationMenu();
    logger.info("App", "Application menu created");
    await createWindow();
    logger.info("App", "Main window created successfully");
    logger.info("App", "Frontend test application startup completed successfully");
  } catch (error) {
    logger.error("App", "Failed to initialize application", error instanceof Error ? error : new Error(String(error)));
    dialog.showErrorBox(
      "Application Startup Error",
      `Failed to start Woodpecker API Frontend Test: ${error instanceof Error ? error.message : String(error)}

The application will now exit.`
    );
    app.quit();
  }
});
app.on("window-all-closed", () => {
  logger.info("App", "All windows closed");
  win = null;
  if (process.platform !== "darwin") {
    logger.info("App", "Quitting application");
    app.quit();
  }
});
app.on("second-instance", () => {
  logger.info("App", "Second instance detected, focusing main window");
  if (win) {
    if (win.isMinimized()) {
      win.restore();
    }
    if (!win.isVisible()) {
      win.show();
    }
    win.focus();
  }
});
app.on("activate", () => {
  logger.info("App", "Application activated");
  const allWindows = BrowserWindow.getAllWindows();
  if (allWindows.length) {
    allWindows[0].focus();
  } else {
    createWindow().catch((error) => {
      logger.error("App", "Failed to recreate window on activate", error instanceof Error ? error : new Error(String(error)));
    });
  }
});
let isShuttingDown = false;
async function gracefulShutdown(signal) {
  if (isShuttingDown) {
    logger.warn("App", `Received ${signal} during shutdown, forcing exit`);
    process.exit(1);
  }
  isShuttingDown = true;
  logger.info("App", `Received ${signal}, initiating graceful shutdown`);
  try {
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach((window) => {
      if (!window.isDestroyed()) {
        window.close();
      }
    });
    await new Promise((resolve) => setTimeout(resolve, 1e3));
    logger.info("App", "Graceful shutdown completed");
    app.quit();
  } catch (error) {
    logger.error("App", "Error during graceful shutdown", error instanceof Error ? error : new Error(String(error)));
    process.exit(1);
  }
}
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
