<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleExecutable</key>
    <string>ReactiveObjC</string>
    <key>CFBundleIdentifier</key>
    <string>com.electron.reactive</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>ReactiveObjC</string>
    <key>CFBundlePackageType</key>
    <string>FMWK</string>
    <key>CFBundleShortVersionString</key>
    <string>3.1.0</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>0.0.0</string>
    <key>DTCompiler</key>
    <string>com.apple.compilers.llvm.clang.1_0</string>
    <key>DTSDKBuild</key>
    <string>23F73</string>
    <key>DTSDKName</key>
    <string>macosx14.5</string>
    <key>DTXcode</key>
    <string>1540</string>
    <key>DTXcodeBuild</key>
    <string>15F31d</string>
    <key>NSHumanReadableCopyright</key>
    <string>Copyright © 2014 GitHub. All rights reserved.</string>
    <key>NSPrincipalClass</key>
    <string/>
    <key>ElectronAsarIntegrity</key>
    <dict>
      <key>Resources/app.asar</key>
      <dict>
        <key>algorithm</key>
        <string>SHA256</string>
        <key>hash</key>
        <string>a99cda4a91a706561f1f6aa6359c9ea2401daf48cef6a1d6be1dcc3545732ad0</string>
      </dict>
    </dict>
  </dict>
</plist>